import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/models/game_state.dart';
import '../../core/providers/game_providers.dart';
import '../../core/providers/app_providers.dart';
import '../../core/services/game_feedback_service.dart';
import '../dialogs/game_over_dialog.dart';
import 'game_ui_overlay.dart';

/// Abstract base class for all games in TapVerse
/// Provides common functionality like scoring, pause/resume, game over handling,
/// sound/vibration integration, and token rewards
abstract class BaseGameWidget extends ConsumerStatefulWidget {
  final GameConfig config;
  final VoidCallback? onGameComplete;
  final VoidCallback? onGameExit;

  const BaseGameWidget({
    super.key,
    required this.config,
    this.onGameComplete,
    this.onGameExit,
  });

  @override
  ConsumerState<BaseGameWidget> createState() => _BaseGameWidgetState();

  /// Override this method to build your game's content
  /// This is where you implement your specific game logic and UI
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState);

  /// Override this method to handle game-specific initialization
  /// Called when the game starts
  void onGameStart(WidgetRef ref) {}

  /// Override this method to handle game-specific pause logic
  /// Called when the game is paused
  void onGamePause(WidgetRef ref) {}

  /// Override this method to handle game-specific resume logic
  /// Called when the game is resumed
  void onGameResume(WidgetRef ref) {}

  /// Override this method to handle game-specific cleanup
  /// Called when the game ends or widget is disposed
  void onGameEnd(WidgetRef ref) {}

  /// Override this method to handle custom game updates
  /// Called every frame when the game is playing
  void onGameUpdate(WidgetRef ref, Duration deltaTime) {}

  /// Helper method to trigger game feedback
  /// Can be called from game implementations
  void triggerFeedback(WidgetRef ref, GameEvent event, {FeedbackConfig? customConfig}) {
    final feedbackService = ref.read(gameFeedbackServiceProvider);
    feedbackService.triggerFeedback(event, customConfig: customConfig);
  }

  /// Helper method to trigger combo feedback
  void triggerComboFeedback(WidgetRef ref, int comboCount) {
    final feedbackService = ref.read(gameFeedbackServiceProvider);
    feedbackService.triggerComboFeedback(comboCount);
  }

  /// Helper method to trigger countdown feedback
  void triggerCountdownFeedback(WidgetRef ref, int secondsRemaining) {
    final feedbackService = ref.read(gameFeedbackServiceProvider);
    feedbackService.triggerCountdownFeedback(secondsRemaining);
  }
}

class _BaseGameWidgetState extends ConsumerState<BaseGameWidget>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  DateTime? _lastFrameTime;
  bool _isInitialized = false;


  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // Initialize the game after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeGame();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _fadeController.dispose();
    _scaleController.dispose();
    
    // Clean up game-specific resources
    widget.onGameEnd(ref);
    
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    final gameState = ref.read(gameStateProvider(widget.config));
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        if (gameState.isPlaying) {
          gameNotifier.pauseGame();
        }
        break;
      case AppLifecycleState.resumed:
        // Don't auto-resume, let user manually resume
        break;
      case AppLifecycleState.detached:
        widget.onGameEnd(ref);
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  void _initializeGame() {
    if (_isInitialized) return;
    
    _isInitialized = true;
    _fadeController.forward();
    
    // Initialize sound service
    ref.read(soundServiceProvider).initialize();
    ref.read(vibrationServiceProvider).initialize();
  }

  void _startGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.startGame();
    widget.onGameStart(ref);
    _startGameLoop();
  }

  void _pauseGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.pauseGame();
    widget.onGamePause(ref);
  }

  void _resumeGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.resumeGame();
    widget.onGameResume(ref);
    _startGameLoop();
  }

  void _endGame() {
    final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
    gameNotifier.endGame();
    widget.onGameEnd(ref);
  }

  void _startGameLoop() {
    _lastFrameTime = DateTime.now();
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(widget.config));
      if (!gameState.isPlaying) return;
      
      final now = DateTime.now();
      final deltaTime = _lastFrameTime != null 
          ? now.difference(_lastFrameTime!) 
          : Duration.zero;
      _lastFrameTime = now;
      
      // Call game-specific update
      widget.onGameUpdate(ref, deltaTime);
      
      // Schedule next frame
      WidgetsBinding.instance.addPostFrameCallback((_) => gameLoop());
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) => gameLoop());
  }

  void _showGameOverDialog(GameState gameState) async {
    final dialog = await GameOverDialog.create(
      ref: ref,
      config: widget.config,
      score: gameState.score,
      onPlayAgain: () {
        final gameNotifier = ref.read(gameStateProvider(widget.config).notifier);
        gameNotifier.resetGame();
        _startGame();
      },
      onHome: () {
        widget.onGameExit?.call();
      },
      onLeaderboard: () {
        // TODO: Navigate to leaderboard
        widget.onGameComplete?.call();
      },
    );

    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => dialog,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(widget.config));
    
    // Show game over dialog when game ends
    ref.listen(gameStateProvider(widget.config), (previous, current) {
      if (previous?.status != GameStatus.gameOver && 
          current.status == GameStatus.gameOver) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showGameOverDialog(current);
        });
      }
    });

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeController,
          child: Stack(
            children: [
              // Game content
              Positioned.fill(
                child: widget.buildGameContent(context, ref, gameState),
              ),
              
              // Game UI overlay (score, pause button, etc.)
              Positioned.fill(
                child: GameUIOverlay(
                  config: widget.config,
                  onPause: _pauseGame,
                  onResume: _resumeGame,
                  onStart: _startGame,
                  onExit: widget.onGameExit,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
